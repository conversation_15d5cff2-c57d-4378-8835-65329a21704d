{"name": "learning-knowledge-system", "version": "1.0.0", "description": "A full-stack application for processing text with AI to generate learning materials", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ai", "learning", "anki", "text-processing"], "author": "", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.2.1", "axios": "^1.12.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "fs-extra": "^11.1.1", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1"}}